import os
import arrow
from bson import ObjectId
from fastapi import UploadFile
from apps.utils import singleton
from motor.core import AgnosticCollection
from apps.db import MongoDB
import apps.models.material as MaterialModel
from apps.utils import make_uuid
from apps.config import get_settings
from apps.models.common import ObjectIdStr
from apps.common import HttpResp, AppException


COLL_MATERIAL: AgnosticCollection = MongoDB.get_collection("material")
COLL_MATERIAL_CATE: AgnosticCollection = MongoDB.get_collection("material_cate")


async def get_next_id():
    """获取下一个现象ID"""
    cursor = COLL_MATERIAL_CATE.find().sort("_id", -1).limit(1)
    data = await cursor.to_list(None)
    if not data:
        return 1
    return int(data[0]["_id"]) + 1


async def get_cate_list(type: int = 1):
    # type 0-全部 1-图片 2-视频
    if type == 0:
        data = await COLL_MATERIAL_CATE.find().to_list(length=None)
    data = await COLL_MATERIAL_CATE.find({"type": type}).to_list(length=None)
    return [MaterialModel.MaterialCateOut(**item) for item in data]


async def add_cate(data: MaterialModel.MaterialCate):
    cate_json = data.model_dump()
    cate_json.update(
        {
            "_id": await get_next_id(),
            "create_time": arrow.utcnow().datetime,
            "update_time": arrow.utcnow().datetime,
        }
    )
    await COLL_MATERIAL_CATE.insert_one(cate_json)
    return {"msg": "添加成功"}


async def delete_cate(id: int):
    await COLL_MATERIAL_CATE.delete_one({"_id": id})
    return {"msg": "删除成功"}


async def update_cate(id: int, data: MaterialModel.MaterialCateEdit):
    update_data = {"name": data.name, "update_time": arrow.utcnow().datetime}
    result = await COLL_MATERIAL_CATE.update_one({"_id": id}, {"$set": update_data})
    return result.modified_count > 0


def get_material_url(filename: str, folder: str):
    ext = filename.split(".")[-1].lower()
    uuid = make_uuid()
    date = arrow.utcnow().format("YYYYMMDD")
    return folder + "/" + date + "/" + uuid + "." + ext


async def upload_material(file: UploadFile, type: int = 1, cid: int = 0):
    dir = "image" if type == 1 else "video" if type == 2 else "other"

    url = get_material_url(str(file.filename), dir)
    file_json = {
        "cid": cid,
        "type": type,
        "name": file.filename,
        "url": url,
        "size": file.size,
        "ext": str(file.filename).split(".")[-1],
        "create_time": arrow.utcnow().datetime,
    }

    file_location = os.path.join(get_settings().upload_directory, url).replace("\\", "/")
    file_path = os.path.dirname(file_location)

    if not os.path.exists(file_path):
        os.makedirs(file_path)
    with open(file_location, "wb") as f:
        f.write(await file.read())

    thumbnail = await get_thumbnail(url, type)
    file_json["thumbnail"] = thumbnail

    result = await COLL_MATERIAL.insert_one(file_json)
    return {"msg": "上传成功", "url": url, "id": str(result.inserted_id)}


async def get_thumbnail(url: str, type: int = 1):
    if not url:
        return ""

    file_path = os.path.join(get_settings().upload_directory, url).replace("\\", "/")
    thumbnail = f"{file_path}.thumbnail.jpg"
    if not os.path.exists(thumbnail):
        try:
            if type == 1:
                from PIL import Image

                image = Image.open(file_path)
                image.thumbnail((150, 150))
                rgb_image = image.convert("RGB")
                rgb_image.save(thumbnail, format="JPEG")
            elif type == 2:
                import cv2

                cap = cv2.VideoCapture(file_path)
                ret, frame = cap.read()
                if not ret:
                    return ""
                frame = cv2.resize(frame, (150, 150))
                cv2.imwrite(thumbnail, frame)
        except Exception as e:
            print(f"get_thumbnail error: {e}")
            return ""
    return f"{url}.thumbnail.jpg"


async def get_material_list(query: MaterialModel.MaterialQuery):
    filter = {"cid": query.cid, "type": query.type}
    if query.cid == None:
        filter = {"type": query.type}
    data = await COLL_MATERIAL.find(filter).to_list(length=None)
    count = await COLL_MATERIAL.count_documents(filter)
    return {"count": count, "lists": [MaterialModel.MaterialOut(**item) for item in data]}


async def get_material_data_by_id(material_id: ObjectIdStr, type: str = ""):
    material_data = await COLL_MATERIAL.find_one({"_id": ObjectId(material_id)})
    if material_data is None:
        raise AppException(HttpResp.VIDEO_NOT_FOUND, "material info not found")
    if type == "cover":
        return os.path.join(get_settings().upload_directory, material_data["thumbnail"]).replace("\\", "/")
    else:
        return os.path.join(get_settings().upload_directory, material_data["url"]).replace("\\", "/")


async def delete_material(material_ids: list[ObjectIdStr]):
    res = await COLL_MATERIAL.delete_many({"_id": {"$in": material_ids}})
    if res.deleted_count > 0:
        return {"msg": "删除成功"}
    else:
        return {"msg": "删除失败"}