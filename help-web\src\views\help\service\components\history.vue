<template>
  <div class="edit-popup">
    <popup
      ref="historyRef"
      title="履歴"
      :async="true"
      width="900px"
      :confirmButtonText="$t('stream.确定')"
      :cancelButtonText="$t('stream.取消')"
    >
      <el-card class="!border-none" shadow="never">
        <el-form ref="formRef" @submit.native.prevent class="mb-[-16px]" :model="queryParams" :inline="true">
          <el-form-item label="顧客情報">
            <el-input v-model="queryParams.keyword" placeholder="顧客名/連絡先" @keyup.enter="resetPage" />
          </el-form-item>

          <el-form-item label="ステータス">
            <el-select class="!w-28" v-model="queryParams.status">
              <el-option v-for="(item, key) in SessionStatus" :key="key" :label="item" :value="Number(key)" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="resetPage">検索</el-button>
            <el-button @click="resetParams">リセット</el-button>
          </el-form-item>
        </el-form>
      </el-card>
      <el-table :data="pager.lists" size="large">
        <el-table-column type="index" />
        <el-table-column align="center" label="顧客" prop="name" />
        <el-table-column align="center" label="連絡先" prop="phone" />
        <el-table-column label="操作台" prop="device_control_name" />
        <el-table-column label="重機側" prop="device_vehicle_name" />
        <el-table-column align="center" label="ステータス" prop="status" min-width="100">
          <template #default="{ row }">
            <el-tag :type="TagStatus[row.status]">{{ SessionStatus[row.status] }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="作成時間" prop="create_time" min-width="120">
          <template #default="{ row }">
            {{ dayjs(row.create_time).local().format("YYYY-MM-DD HH:mm:ss") }}
          </template>
        </el-table-column>
        <el-table-column label="解決時間" prop="completion_time" min-width="120">
          <template #default="{ row }">
            {{ row.completion_time ? dayjs(row.completion_time).local().format("YYYY-MM-DD HH:mm:ss") : "--" }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="対応策" prop="status" min-width="120">
          <template #default="{ row }">
            {{ operationList.find((item: any) => item.id == row.resolve)?.content }}</template
          >
        </el-table-column>
        <el-table-column align="center" label="詳細情報" fixed="right">
          <template #default="{ row }">
            <div class="flex flex-row justify-center items-center">
              <!-- <el-button class="margin-left0" type="primary" link @click=""> 删除记录 </el-button> -->
              <el-button class="margin-left0" type="primary" link @click="handleDetail(row)"> ケース詳細 </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="flex justify-end mt-4">
        <pagination v-model="pager" @change="getLists" />
      </div>
    </popup>
    <edit-popup v-if="showEdit" ref="editRef" @close="showEdit = false" />
  </div>
</template>
<script lang="ts" setup>
import feedback from "@/utils/feedback";
import Popup from "@/components/popup/index.vue";
import { orderList as getOrderListApi } from "@/api/customer-ja";
import EditPopup from "../../order/edit.vue";
import { usePaging } from "@/hooks/usePaging";

import { storeToRefs } from "pinia";
import useCustomerJaStore from "@/stores/modules/customer-ja";
const customerStore = useCustomerJaStore();
const { operationList } = storeToRefs(customerStore);

const emit = defineEmits(["success", "close", "add"]);
const historyRef = shallowRef<InstanceType<typeof Popup>>();

const SessionStatus: any = {
  1: "未対応",
  2: "対応中",
  3: "解決済",
  4: "対応待ち",
};
const TagStatus: any = {
  1: "info",
  2: "warning",
  3: "success",
  4: "danger",
};

const queryParams = reactive({
  keyword: "",
  status: 3,
});

const { pager, getLists, resetPage, resetParams } = usePaging({
  fetchFun: getOrderListApi,
  params: queryParams,
});

const open = async (type = "add") => {
  await getLists();
  historyRef.value?.open();
};

const showEdit = ref(false);
const editRef = shallowRef<InstanceType<typeof EditPopup>>();

const handleDetail = async (row: any) => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open("detail");
  editRef.value?.setFormData(row);
};

const handleClose = () => {
  emit("close");
};

defineExpose({
  open,
});
</script>
