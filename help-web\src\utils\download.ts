/**
 * 文件下载工具函数
 * 支持强制下载各种文件类型，避免浏览器预览
 */

import { ElLoading } from 'element-plus';
import feedback from '@/utils/feedback';

/**
 * 根据 MIME 类型获取文件扩展名
 */
export function getMimeTypeExtension(mimeType: string): string {
  const mimeToExt: Record<string, string> = {
    'image/jpeg': '.jpg',
    'image/png': '.png',
    'image/gif': '.gif',
    'image/webp': '.webp',
    'image/svg+xml': '.svg',
    'image/bmp': '.bmp',
    'image/tiff': '.tiff',
    'video/mp4': '.mp4',
    'video/avi': '.avi',
    'video/quicktime': '.mov',
    'video/x-msvideo': '.avi',
    'video/x-ms-wmv': '.wmv',
    'video/x-flv': '.flv',
    'video/webm': '.webm',
    'video/x-matroska': '.mkv',
    'video/3gpp': '.3gp',
    'audio/mpeg': '.mp3',
    'audio/wav': '.wav',
    'audio/ogg': '.ogg',
    'audio/aac': '.aac',
    'audio/flac': '.flac',
    'audio/x-ms-wma': '.wma',
    'audio/mp4': '.m4a',
    'application/pdf': '.pdf',
    'application/zip': '.zip',
    'application/x-rar-compressed': '.rar',
    'application/x-7z-compressed': '.7z',
    'text/plain': '.txt',
    'text/html': '.html',
    'text/css': '.css',
    'text/javascript': '.js',
    'application/json': '.json',
    'application/xml': '.xml',
    'application/msword': '.doc',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
    'application/vnd.ms-excel': '.xls',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
    'application/vnd.ms-powerpoint': '.ppt',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': '.pptx',
  };
  
  return mimeToExt[mimeType] || '';
}

/**
 * 从响应头中提取文件名
 */
export function extractFileNameFromResponse(response: Response, fallbackName: string): string {
  let fileName = fallbackName;
  
  // 尝试从响应头获取文件名
  const contentDisposition = response.headers.get('content-disposition');
  if (contentDisposition) {
    const fileNameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
    if (fileNameMatch && fileNameMatch[1]) {
      fileName = fileNameMatch[1].replace(/['"]/g, '');
      // 处理 URL 编码的文件名
      try {
        fileName = decodeURIComponent(fileName);
      } catch (e) {
        console.warn('文件名解码失败:', e);
      }
    }
  }
  
  return fileName;
}

/**
 * 确保文件名有正确的扩展名
 */
export function ensureFileExtension(fileName: string, url: string, mimeType: string): string {
  // 如果文件名已经有扩展名，直接返回
  if (fileName.includes('.')) {
    return fileName;
  }
  
  // 尝试从 URL 获取扩展名
  try {
    const urlPath = new URL(url).pathname;
    const urlFileName = urlPath.split('/').pop();
    if (urlFileName && urlFileName.includes('.')) {
      const extension = '.' + urlFileName.split('.').pop();
      return fileName + extension;
    }
  } catch (e) {
    console.warn('从 URL 提取扩展名失败:', e);
  }
  
  // 根据 MIME 类型添加扩展名
  const extension = getMimeTypeExtension(mimeType);
  if (extension) {
    return fileName + extension;
  }
  
  return fileName;
}

/**
 * 使用 Blob 强制下载文件
 * 这种方法可以避免浏览器预览文件，强制触发下载
 */
export async function downloadFileWithBlob(
  url: string, 
  fileName: string = 'download',
  options: {
    headers?: Record<string, string>;
    showLoading?: boolean;
    loadingText?: string;
  } = {}
): Promise<void> {
  const { headers = {}, showLoading = true, loadingText = '正在下载文件...' } = options;
  
  if (!url) {
    feedback.msgError('文件链接无效，无法下载');
    return;
  }

  let loadingInstance: any = null;
  
  try {
    // 显示下载提示
    if (showLoading) {
      loadingInstance = ElLoading.service({
        text: loadingText,
        background: 'rgba(0, 0, 0, 0.7)',
      });
    }

    // 使用 fetch 获取文件数据
    const response = await fetch(url, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // 获取文件数据
    const blob = await response.blob();
    
    // 处理文件名
    let finalFileName = extractFileNameFromResponse(response, fileName);
    finalFileName = ensureFileExtension(finalFileName, url, blob.type);

    // 创建下载链接
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = finalFileName;
    link.style.display = 'none';

    // 添加到 DOM 并触发下载
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);

    if (loadingInstance) {
      loadingInstance.close();
    }
    feedback.msgSuccess('文件下载成功');
    
  } catch (error) {
    console.error('下载文件失败:', error);
    
    if (loadingInstance) {
      loadingInstance.close();
    }
    
    // 如果 fetch 失败，尝试使用传统方法作为后备
    try {
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      link.style.display = 'none';
      
      // 尝试强制下载的属性
      link.setAttribute('download', fileName);
      link.setAttribute('target', '_blank');
      link.setAttribute('rel', 'noopener noreferrer');
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      feedback.msgSuccess('文件下载已启动');
    } catch (fallbackError) {
      console.error('后备下载方法也失败:', fallbackError);
      feedback.msgError('文件下载失败，请检查网络连接或联系管理员');
    }
  }
}

/**
 * 简化的下载函数，适用于大多数场景
 */
export function downloadFile(url: string, fileName?: string): Promise<void> {
  return downloadFileWithBlob(url, fileName);
}
