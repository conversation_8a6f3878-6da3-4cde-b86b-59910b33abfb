<template>
  <div class="main-wrap">
    <div class="header">
      <div class="logo">
        <img class="logo-img1" src="@/assets/images/logo_english_black.webp" alt="" srcset="" />
        <div class="logo-title">故障診断システム</div>
      </div>
      <div class="bg-[#f4f4f4] flex items-center justify-center rounded-lg">
        <el-dropdown class="px-2" @command="handleCommand">
          <div class="flex items-center">
            <el-avatar :size="36" :src="avatar" />
            <div class="ml-3 mr-2 text-2xl text-[#333]">{{ userInfo.nickname }}</div>
            <icon name="el-icon-ArrowDown" />
          </div>

          <template #dropdown>
            <el-dropdown-menu>
              <router-link to="/order">
                <el-dropdown-item>管理ページへ</el-dropdown-item>
              </router-link>
              <el-dropdown-item command="logout">{{ $t("common.退出登录") }}</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <div class="content">
      <div class="w-[240px] bg-white px-2 py-4" shadow="hover">
        <div class="title flex justify-between">
          <span>問い合わせリスト</span>
          <el-button type="primary" size="default" link @click="openHistory()">履歴</el-button>
        </div>
        <div class="session">
          <div class="item cursor-pointer" @click="openAddSession">
            <div class="title mx-auto h-10 leading-10">新規作成</div>
          </div>
          <div
            class="item"
            v-for="item in pageData.orderList"
            :class="{
              'active-item': item.id == pageData.currentSession.id,
            }"
            @click="handleChangeSession(item)"
          >
            <div class="name">
              <div>{{ item.customer_name }}</div>
              <div>
                <el-tag :type="TagStatus[item.status]">{{ SessionStatus[item.status] }}</el-tag>
              </div>
            </div>
            <div class="mt-1">
              <span class="mr-1">{{ item.person_name || "--" }}</span>
              <span>{{ item.contact_info || "--" }}</span>
            </div>
            <div class="time">
              {{ dayjs(item.create_time).local().format("YYYY-MM-DD HH:mm:ss") }}
            </div>
          </div>
        </div>
      </div>
      <div class="relative mx-2 flex-1 flex flex-col">
        <el-card class="question !border-none" shadow="hover">
          <div class="title mb-8">故障現象を選択してください:</div>
          <div
            class="mt-3 flex items-center"
            key="key"
            v-for="(item, key, index) in pageData.phenomenon_id"
          >
            現象{{ index + 1 }}:&nbsp
            <el-tree-select
              v-model="pageData.phenomenon_id[key]"
              :data="pageData.phenomenonData"
              placeholder="現象を選択してください"
              filterable
              :filter-method="filterMethod"
              :props="{ value: 'id', label: 'content' }"
              style="width: 440px"
              :disabled="
                pageData.currentSession.status === 3 || pageData.currentSession.status === 4
              "
              @change="handleAnswerChange"
            >
              <template #default="{ data: { content, description } }">
                <div class="">
                  {{ content }}
                  <el-tooltip effect="light" placement="top" :content="description">
                    <el-button v-if="description" type="primary" size="small" link>詳細</el-button>
                  </el-tooltip>
                </div>
              </template>
            </el-tree-select>
            <icon
              v-if="index > 0 && pageData.currentSession.status !== 3"
              :size="20"
              name="el-icon-Delete"
              class="ml-2 cursor-pointer"
              @click="handleDeleteQuestion(key)"
            />
          </div>
          <div
            v-if="pageData.currentSession.status === 1 || pageData.currentSession.status === 2"
            class="mt-5"
          >
            <el-button type="primary" size="default" @click="handleAddQuestion"
              >現象を追加</el-button
            >
            <el-button type="primary" size="default" @click="handleSearchAnswer"
              >対策を検索</el-button
            >
          </div>
        </el-card>

        <el-card class="answer !border-none mt-8" shadow="hover">
          <div class="title">
            以下の手順に従ってチェックを行って、操作台を再起動して故障が解決できるかどうかを確認してください。
            <br /><span class="font-medium text-gray"
              >提示: 実施した手順にチェックを入れてください</span
            >
          </div>
          <div v-if="pageData.operation_arr.length > 0" class="answer-content1">
            <el-checkbox-group
              v-model="pageData.currentSession.operation_checked"
              :disabled="pageData.currentSession.status !== 2"
              @change="handleCheckedChange"
            >
              <div class="answer-item" v-for="(item, index) in pageData.operation_arr">
                <div>
                  <el-checkbox :value="item.id" @change="handleCheckedItemChange(item, $event)" />
                  <span class="answer-index">{{ index + 1 }}:</span>
                  {{ item.content }}
                  <el-tag size="small" type="warning" effect="light" round>
                    {{ DEVICE_TYPE_JA[item.device_type] }}
                  </el-tag>
                  <el-tag class="ml-1" size="small" type="info" effect="light" round>
                    {{ ANSWER_TYPE_JA[item.answer_type] }}
                  </el-tag>
                  <el-tooltip
                    class="box-item"
                    effect="light"
                    :content="item.is_need_detail"
                    placement="top-start"
                  >
                    <el-tag
                      v-if="item.is_need_tools"
                      class="ml-1 cursor-pointer"
                      size="small"
                      type="success"
                      effect="light"
                      round
                    >
                      ツール必要
                    </el-tag>
                  </el-tooltip>
                </div>
                <div class="answer-description text-[14px]" v-if="item.description">
                  {{ item.description }}
                </div>
              </div>
            </el-checkbox-group>
            <div class="answer-item pb-10" v-if="pageData.operation_arr.length > 0">
              <p v-if="pageData.currentSession.status == 2">
                <span class="answer-index">最後:</span>もし上記の操作で問題が解決できない場合、<span
                  class="text-primary cursor-pointer"
                  @click="openFeedback('feedback')"
                  >BuilderX</span
                >にご連絡してください。
              </p>
              <p v-if="pageData.currentSession.status == 4">
                <span class="answer-index">BuilderXが至急対応中です。</span>
              </p>
            </div>
          </div>
          <div v-else class="flex justify-center w-full">
            <el-empty description="検索してから対応策を確認してください" />
          </div>
          <div
            v-if="pageData.currentSession.status == 2 || pageData.currentSession.status == 4"
            class="absolute bottom-4 right-4"
          >
            <el-button
              title="解決完了"
              size="large"
              type="success"
              :icon="Finished"
              @click="openFeedback('resolve')"
            >
              解決完了
            </el-button>
          </div>
        </el-card>
      </div>
      <div class="w-[240px] bg-white pl-4 py-4 flex flex-col h-full" shadow="hover">
        <div class="title flex justify-between">
          <span>ケース詳細</span>
          <el-button type="primary" class="mr-4" size="default" link @click="openFeedback('edit')">
            編集
          </el-button>
        </div>
        <el-descriptions class="mt-4" :column="1">
          <el-descriptions-item label="顧客名">{{
            pageData.currentSession.customer_name
          }}</el-descriptions-item>
          <el-descriptions-item label="連絡先">{{
            pageData.currentSession.contact_info
          }}</el-descriptions-item>
          <el-descriptions-item label="操作台">{{
            pageData.currentSession.device_control_name
          }}</el-descriptions-item>
          <el-descriptions-item label="重機側">{{
            pageData.currentSession.device_vehicle_name
          }}</el-descriptions-item>
          <el-descriptions-item label="備考">{{
            pageData.currentSession.status_remark || "--"
          }}</el-descriptions-item>
          <el-descriptions-item label="ステータス">
            <el-tag :type="TagStatus[pageData.currentSession.status]">{{
              SessionStatus[pageData.currentSession.status]
            }}</el-tag>
          </el-descriptions-item>
        </el-descriptions>
        <div class="flex overflow-x-auto mb-2 mr-4">
          {{ pageData.currentSession.imgUrlList }}
          <!-- <el-image
            v-if="pageData.currentSession.imgUrlList.length > 0"
            v-for="(url, index) in pageData.currentSession.imgUrlList.map((item) => item.url)"
            style="width: 50px; height: 50px; margin-right: 10px; flex-shrink: 0"
            class="feedback-image"
            :key="url"
            :src="url"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="pageData.currentSession.imgUrlList.map((item) => item.url)"
            :initial-index="index"
            fit="cover"
            show-progress
          /> -->
        </div>

        <div class="title text-lg font-semibold mb-3 my-2">タイムライン</div>
        <el-timeline style="max-width: 220px; flex: 1 1 0%; min-height: 0; overflow-y: auto">
          <el-timeline-item
            v-for="(activity, index) in pageData.currentSession.timeline"
            :key="index"
            :timestamp="
              dayjs(activity.timestamp || Date.now())
                .local()
                .format('YYYY-MM-DD HH:mm:ss')
            "
          >
            {{ activity.content }}
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>
  </div>

  <AddSession
    v-if="addSessionShow"
    ref="addSessionRef"
    :operation_arr="pageData.operation_arr"
    @add="handleAddSession"
    @close="addSessionShow = false"
    @resolve="initData"
    @feedback="handleFeedback"
    @edit="handleEditSession"
  />
  <Feedback v-if="feedbackShow" ref="feedbackRef" @close="feedbackShow = false" />
</template>
<script lang="ts" setup name="post">
import { storeToRefs } from "pinia";
import useCustomerJaStore from "@/stores/modules/customer-ja";
import feedback from "@/utils/feedback";
import { ANSWER_TYPE_JA, DEVICE_TYPE_JA } from "@/utils/constants";
import AddSession from "../order/edit.vue";
import Feedback from "./components/history.vue";
import { orderList as getOrderListApi, orderEdit } from "@/api/customer-ja";
import { Finished, Phone } from "@element-plus/icons-vue";
import type { LocaleType } from "@/locales/lang";
import { useLocale } from "@/locales/useLocale";

import useUserStore from "@/stores/modules/user";
import avatar from "@/assets/images/avatar.png";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const userStore = useUserStore();

const userInfo = computed(() => userStore.userInfo);

const handleCommand = async (command: string) => {
  switch (command) {
    case "logout":
      await feedback.confirm(t("common.确定退出登录吗"));
      userStore.logout();
  }
};

const { changeLocale } = useLocale();

const addSessionRef = shallowRef<InstanceType<typeof AddSession>>();
const addSessionShow = ref(false);
const feedbackRef = shallowRef<InstanceType<typeof Feedback>>();
const feedbackShow = ref(false);
const customerStore = useCustomerJaStore();
const { phenomenonList, operationList } = storeToRefs(customerStore);

// 默认未解决 搜索后待反馈 反馈后已解决
const SessionStatus: any = {
  1: "未対応",
  2: "対応中",
  3: "解決済",
  4: "BX対応中",
};

const TagStatus: any = {
  1: "success",
  2: "warning",
  3: "success",
  4: "danger",
};

const pageData: any = reactive({
  currentId: 3,
  currentSession: {
    timeline: [
      {
        content: "",
        timestamp: "",
      },
    ],
    operation_checked: [],
    imgUrlList: [],
  },
  phenomenonData: [],
  phenomenon_id: {
    id_1: "",
  },
  operation_arr: [],
  orderList: [],
});

watch(phenomenonList, (newValue) => {
  if (newValue) {
    pageData.phenomenonData = newValue;
  }
});

const handleChangeSession = async (item: any) => {
  pageData.currentSession = item;
  console.log(pageData.currentSession);

  pageData.phenomenon_id = { id_1: "" };

  item.phenomenon_id.forEach((item: any, index: number) => {
    pageData.phenomenon_id[`id_${index + 1}`] = item;
  });

  pageData.operation_arr = operationList.value
    .filter((operation) => {
      return item.operation_id.includes(operation.id);
    })
    .sort((a, b) => {
      if (a.device_type !== b.device_type) {
        return a.device_type - b.device_type;
      }
      return a.answer_type - b.answer_type;
    });

  if (pageData.operation_arr.length > 0) {
    let a = operationList.value.find((operation) => operation.id === "S2001");
    let b = operationList.value.find((operation) => operation.id === "S2002");

    pageData.operation_arr.unshift(a);
    pageData.operation_arr.push(b);
  }

  handleAnswerChange();
};

const filterMethod = (keyword: string) => {
  pageData.phenomenonData = searchByKeyword(phenomenonList.value, keyword);
};

const searchByKeyword = (data: any, keyword: any) => {
  let result: any = [];
  data.forEach((item: any) => {
    let res = item.children.filter((child: any) => {
      return child.content.includes(keyword) || child.description.includes(keyword);
    });

    if (res.length > 0) {
      result.push({ ...item, children: res });
    }
  });
  return result;
};

const openAddSession = async () => {
  addSessionShow.value = true;
  await nextTick();
  addSessionRef.value?.open("add");
};

const openFeedback = async (type: string) => {
  if (pageData.currentSession.status == 3) return;
  addSessionShow.value = true;
  await nextTick();
  addSessionRef.value?.open(type);
  console.log(pageData.currentSession);
  addSessionRef.value?.setFormData(pageData.currentSession);
};

const handleAddSession = async (row: any) => {
  await getOrderList();
  await handleChangeSession(row);
};

const handleCheckedChange = (value: any) => {};

const handleCheckedItemChange = async (row: any, event: any) => {
  if (event) {
    pageData.currentSession.timeline_content = `操作: ${
      operationList.value.find((item) => item.id === row.id).content
    }`;
  } else {
    pageData.currentSession.timeline_content = `操作取り消し: ${
      operationList.value.find((item) => item.id === row.id).content
    }`;
  }

  const { content } = await orderEdit(pageData.currentSession);
  pageData.currentSession.timeline_content = "";
  pageData.currentSession.timeline = content.timeline;
  await getOrderList();
};

const handleAnswerChange = () => {
  const phenomenonIds = Object.values(pageData.phenomenon_id);
  phenomenonList.value.forEach((item: any) => {
    item.children.forEach((child: any) => {
      if (phenomenonIds.includes(child.id)) {
        child.disabled = true;
      } else {
        child.disabled = false;
      }
    });
  });
};

const openHistory = async (type = "add") => {
  feedbackShow.value = true;
  await nextTick();
  feedbackRef.value?.open();
};

const handleAddQuestion = () => {
  const contentCount = Object.keys(pageData.phenomenon_id).length;
  if (contentCount > 2) {
    feedback.msgWarning("故障現象が多いため、BuilderXまでご連絡ください。");
    return;
  }
  nextTick(() => {
    pageData.phenomenon_id[`id_${contentCount + 1}`] = "";
  });
};

const handleDeleteQuestion = (key: any) => {
  delete pageData.phenomenon_id[key];
  let ids = JSON.parse(JSON.stringify(pageData.phenomenon_id));

  pageData.phenomenon_id = { id_1: "" };
  Object.values(ids).forEach((item: any, index: number) => {
    pageData.phenomenon_id[`id_${index + 1}`] = item;
  });

  handleAnswerChange();
};

const handleSearchAnswer = async () => {
  const searchIds = Object.values(pageData.phenomenon_id);

  if (searchIds.includes("")) {
    return feedback.msgWarning("現象を選択してください");
  }
  if (!pageData.currentSession.id) {
    return feedback.msgWarning("新規ケースを作成してください");
  }

  feedback.loading("");
  await new Promise((resolve) => setTimeout(resolve, 500));
  feedback.closeLoading();

  const uniqueAnswerIds = phenomenonList.value
    .reduce((acc: any, content: any) => {
      content.children.forEach((child: any) => {
        if (searchIds.includes(child.id)) {
          acc = acc.concat(child.operation_id);
        }
      });
      return acc;
    }, [])
    .reduce((acc: any, id: any) => {
      if (!acc.includes(id)) {
        acc.push(id);
      }
      return acc;
    }, []);

  console.log(operationList.value);

  pageData.operation_arr = operationList.value
    .filter((item) => {
      return uniqueAnswerIds.includes(item.id);
    })
    .sort((a, b) => {
      // 首先按 device_type 排序
      if (a.device_type !== b.device_type) {
        return a.device_type - b.device_type;
      }
      // 如果 device_type 相同，按 answer_type 排序
      return a.answer_type - b.answer_type;
    });

  const sortAnswerIds = pageData.operation_arr.map((item: any) => item.id);

  pageData.currentSession.phenomenon_id = searchIds;
  pageData.currentSession.operation_id = sortAnswerIds;
  pageData.currentSession.status = 2;
  const { content } = await orderEdit({
    timeline_content: "対策を検索",
    ...pageData.currentSession,
  });
  pageData.currentSession.timeline = content.timeline;

  let a = operationList.value.find((operation) => operation.id === "S2001");
  let b = operationList.value.find((operation) => operation.id === "S2002");
  console.log(pageData.operation_arr);

  pageData.operation_arr.push(b);
  pageData.operation_arr.unshift(a);

  await getOrderList();
};

const getOrderList = async () => {
  const { lists } = await getOrderListApi({ page_no: 1, page_size: 100, status: 5 });
  pageData.orderList = lists;
};

const initData = async () => {
  await customerStore.getPhenomenonList();
  await customerStore.getOperationList();
  await getOrderList().then(() => {
    if (pageData.orderList.length > 0) {
      handleChangeSession(pageData.orderList[0]);
    } else {
      Object.assign(pageData, {
        phenomenon_id: {
          id_1: "",
        },
        operation_arr: [],
        currentSession: {
          status: 1,
          customer_name: "",
          contact_info: "",
          imgUrlList: [],
          device: "",
          timeline: [{ content: "", timestamp: "" }],
        },
      });
    }
  });
};

const handleFeedback = async (row: any) => {
  pageData.currentSession.timeline_content = "";
  pageData.currentSession.timeline = row.timeline;
  pageData.currentSession.status = row.status;
  await getOrderList();
};

const handleEditSession = async (row: any) => {
  pageData.currentSession = row;
}

onMounted(() => {
  initData();
  changeLocale("ja" as LocaleType);
});
</script>

<style lang="scss" scoped>
.main-wrap {
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  height: 100vh;
  width: 100vw;
  align-items: center;
  // background-image: url(https://gw.alicdn.com/imgextra/i2/O1CN01VdZT3d21FRMJMxogQ_!!6000000006955-2-tps-1400-864.png);
  background: linear-gradient(to top left, #c6c8cb 35%, #eaeaea 65%);
  background-size: 100% 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 900px;
  overflow: hidden;
  justify-content: center;
  // font-family: PingFangSC-Regular, sans-serif;
  flex-direction: column;
  padding: 0 20px;
  font-family: Meiryo, sans-serif;
  .header {
    display: flex;
    width: 100%;
    justify-content: space-between;
    .logo {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 50px;
      .logo-img1 {
        height: 48px;
      }
      .logo-title {
        font-size: 22px;
        font-weight: 800;
        margin-left: 18px;
        letter-spacing: 2px;
      }
    }
  }

  .content {
    background: #eaeaea;
    width: 100%;
    height: 86%;
    margin-top: 20px;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    .title {
      font-size: 16px;
      font-weight: 600;
      letter-spacing: 1px;
    }
  }

  .session {
    margin-top: 20px;
    overflow-y: auto;
    height: 100%;
    padding-bottom: 36px;
    .item {
      margin-top: 8px;
      display: flex;
      flex-direction: column;
      background-color: #ffefe6;
      padding: 8px;
      border-radius: 4px;
      cursor: pointer;
      .name {
        font-size: 13px;
        font-weight: 600;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
      }

      .time {
        display: flex;
        justify-content: flex-end;
        margin-top: 2px;
      }
    }
    .active-item {
      background-color: #ff5e00a6;
    }
  }

  .content {
    transition: all 0.3s linear;
  }
  .answer {
    margin-top: 10px;
    overflow-y: auto;
    display: flex;
    flex: 1;
    transition: all 0.3s linear;

    &-content {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
    }
    &-index {
      font-weight: 600;
    }
    &-item {
      font-size: 16px;
      margin-left: 20px;
      margin-top: 12px;
      color: #000;
    }
    &-description {
      margin-top: 2px;
      margin-left: 42px;
      line-height: 24px;
      color: #666;
    }
  }
}
:deep(.el-radio) {
  margin-right: 0px;
}
:deep(.el-card__body) {
  width: 100%;
}
:deep(.el-descriptions__content) {
  word-break: break-all;
}
</style>
